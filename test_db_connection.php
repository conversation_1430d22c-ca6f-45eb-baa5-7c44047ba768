<?php

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== Test de connexion à la base de données ===\n";
echo "Host: " . $_ENV['DB_HOST'] . "\n";
echo "Port: " . $_ENV['DB_PORT'] . "\n";
echo "Database: " . $_ENV['DB_DATABASE'] . "\n";
echo "Username: " . $_ENV['DB_USERNAME'] . "\n";
echo "Password: " . (empty($_ENV['DB_PASSWORD']) ? '(empty)' : '***') . "\n\n";

try {
    $dsn = "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connexion au serveur MySQL réussie\n";
    
    // Vérifier si la base de données existe
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$_ENV['DB_DATABASE']}'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        echo "✅ Base de données '{$_ENV['DB_DATABASE']}' existe\n";
        
        // Se connecter à la base de données spécifique
        $dsn = "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4";
        $pdo = new PDO($dsn, $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "✅ Connexion à la base de données '{$_ENV['DB_DATABASE']}' réussie\n";
        
        // Lister les tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "\n📋 Tables existantes:\n";
        if (empty($tables)) {
            echo "   Aucune table trouvée\n";
        } else {
            foreach ($tables as $table) {
                echo "   - $table\n";
            }
        }
        
    } else {
        echo "❌ Base de données '{$_ENV['DB_DATABASE']}' n'existe pas\n";
        echo "💡 Création de la base de données...\n";
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$_ENV['DB_DATABASE']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Base de données '{$_ENV['DB_DATABASE']}' créée\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Test terminé ===\n";
