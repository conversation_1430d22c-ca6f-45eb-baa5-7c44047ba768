<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SiteTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
        $this->site = Site::factory()->create([
            'name' => 'Test Site',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);
    }

    /**
     * Test admin can list all sites
     */
    public function test_admin_can_list_sites(): void
    {
        Site::factory()->count(3)->create();
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/sites');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);
    }

    /**
     * Test admin can create a new site
     */
    public function test_admin_can_create_site(): void
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/sites', [
            'name' => 'New Construction Site',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar'],
                    'data' => [
                        'id',
                        'name',
                        'latitude',
                        'longitude'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'name' => 'New Construction Site',
                        'latitude' => 34.0209,
                        'longitude' => -6.8416
                    ]
                ]);

        $this->assertDatabaseHas('sites', [
            'name' => 'New Construction Site',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ]);
    }

    /**
     * Test admin can assign site to employee
     */
    public function test_admin_can_assign_site_to_employee(): void
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/assign-site', [
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar'],
                    'data' => [
                        'user' => ['id', 'name', 'email'],
                        'site' => ['id', 'name', 'latitude', 'longitude']
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);

        $this->assertDatabaseHas('assignments', [
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id
        ]);
    }

    /**
     * Test employee can view their assigned sites
     */
    public function test_employee_can_view_assigned_sites(): void
    {
        // Assign site to employee
        Assignment::create([
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id
        ]);

        $token = $this->employee->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/my-sites');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude'
                        ]
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);

        // Verify the assigned site is in the response
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals($this->site->id, $responseData[0]['id']);
    }

    /**
     * Test employee cannot access admin site endpoints
     */
    public function test_employee_cannot_access_admin_site_endpoints(): void
    {
        $token = $this->employee->createToken('test-token')->plainTextToken;

        // Test cannot list all sites
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/sites');
        $response->assertStatus(403);

        // Test cannot create site
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/sites', [
            'name' => 'Unauthorized Site',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ]);
        $response->assertStatus(403);

        // Test cannot assign site
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/assign-site', [
            'user_id' => $this->employee->id,
            'site_id' => $this->site->id
        ]);
        $response->assertStatus(403);
    }

    /**
     * Test site creation validation
     */
    public function test_site_creation_validation(): void
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        // Test missing name
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/sites', [
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ]);
        $response->assertStatus(422);

        // Test invalid latitude
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/sites', [
            'name' => 'Test Site',
            'latitude' => 100, // Invalid latitude
            'longitude' => -6.8416
        ]);
        $response->assertStatus(422);

        // Test invalid longitude
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/sites', [
            'name' => 'Test Site',
            'latitude' => 34.0209,
            'longitude' => 200 // Invalid longitude
        ]);
        $response->assertStatus(422);
    }

    /**
     * Test site assignment validation
     */
    public function test_site_assignment_validation(): void
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        // Test missing user_id
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/assign-site', [
            'site_id' => $this->site->id
        ]);
        $response->assertStatus(422);

        // Test missing site_id
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/assign-site', [
            'user_id' => $this->employee->id
        ]);
        $response->assertStatus(422);

        // Test non-existent user
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/assign-site', [
            'user_id' => 99999,
            'site_id' => $this->site->id
        ]);
        $response->assertStatus(422);

        // Test non-existent site
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/assign-site', [
            'user_id' => $this->employee->id,
            'site_id' => 99999
        ]);
        $response->assertStatus(422);
    }
}
