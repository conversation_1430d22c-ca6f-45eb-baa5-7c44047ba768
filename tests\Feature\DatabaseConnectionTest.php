<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Verification;
use App\Models\Assignment;
use App\Models\Log;

class DatabaseConnectionTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test database connection
     */
    public function test_database_connection(): void
    {
        // Test basic database connection
        $this->assertTrue(DB::connection()->getPdo() !== null);
        
        // Test that we can query the database
        $result = DB::select('SELECT 1 as test');
        $this->assertEquals(1, $result[0]->test);
    }

    /**
     * Test all models can be created and relationships work
     */
    public function test_models_and_relationships(): void
    {
        // Create test data
        $admin = User::factory()->create(['role' => 'admin']);
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();
        
        // Test User model
        $this->assertInstanceOf(User::class, $admin);
        $this->assertTrue($admin->isAdmin());
        $this->assertFalse($employee->isAdmin());
        $this->assertTrue($employee->isEmployee());
        
        // Test Site model
        $this->assertInstanceOf(Site::class, $site);
        $this->assertIsFloat($site->latitude);
        $this->assertIsFloat($site->longitude);
        
        // Test Assignment relationship
        $assignment = Assignment::create([
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);
        
        $this->assertInstanceOf(Assignment::class, $assignment);
        $this->assertEquals($employee->id, $assignment->user_id);
        $this->assertEquals($site->id, $assignment->site_id);
        
        // Test User-Site relationship through assignments
        $employee->sites()->attach($site->id);
        $this->assertTrue($employee->sites->contains($site));
        $this->assertTrue($site->users->contains($employee));
        
        // Test Pointage model and relationships
        $pointage = Pointage::factory()->create([
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);
        
        $this->assertInstanceOf(Pointage::class, $pointage);
        $this->assertEquals($employee->id, $pointage->user_id);
        $this->assertEquals($site->id, $pointage->site_id);
        $this->assertEquals($employee->id, $pointage->user->id);
        $this->assertEquals($site->id, $pointage->site->id);
        
        // Test Verification model
        $verification = Verification::create([
            'user_id' => $employee->id,
            'latitude' => 33.5731,
            'longitude' => -7.5898,
            'date_heure' => now()
        ]);
        
        $this->assertInstanceOf(Verification::class, $verification);
        $this->assertEquals($employee->id, $verification->user_id);
        
        // Test Log model
        $log = Log::create([
            'user_id' => $employee->id,
            'action' => 'test_action',
            'status' => 'success',
            'details' => ['test' => 'data'],
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent'
        ]);
        
        $this->assertInstanceOf(Log::class, $log);
        $this->assertEquals($employee->id, $log->user_id);
        $this->assertIsArray($log->details);
    }

    /**
     * Test Site location calculation
     */
    public function test_site_location_calculation(): void
    {
        $site = Site::factory()->create([
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);
        
        // Test location within range (same coordinates)
        $this->assertTrue($site->isWithinRange(33.5731, -7.5898, 50));
        
        // Test location outside range
        $this->assertFalse($site->isWithinRange(33.6000, -7.6000, 50));
        
        // Test with larger radius
        $this->assertTrue($site->isWithinRange(33.5731, -7.5898, 1000));
    }

    /**
     * Test Pointage duration calculation
     */
    public function test_pointage_duration_calculation(): void
    {
        $pointage = new Pointage([
            'debut_pointage' => '2024-01-01 08:00:00',
            'fin_pointage' => '2024-01-01 17:00:00'
        ]);
        
        $pointage->calculateDuration();
        $this->assertEquals('09:00:00', $pointage->duree);
    }
}
