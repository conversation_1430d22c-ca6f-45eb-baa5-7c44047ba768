<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EmployeeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
    }

    public function test_admin_can_list_employees()
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/employees');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ]
                    ],
                    'pagination'
                ]);
    }

    public function test_admin_can_create_employee()
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/employees', [
            'name' => 'New Employee',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'employee'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar'],
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ])
                ->assertJson([
                    'success' => true
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);
    }

    public function test_admin_can_view_employee()
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/employees/' . $this->employee->id);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ]);
    }

    public function test_admin_can_update_employee()
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->putJson('/api/employees/' . $this->employee->id, [
            'name' => 'Updated Name',
            'email' => $this->employee->email,
            'role' => 'employee'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar'],
                    'data' => [
                        'id', 'name', 'email', 'role'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'name' => 'Updated Name'
                    ]
                ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->employee->id,
            'name' => 'Updated Name'
        ]);
    }

    public function test_admin_can_delete_employee()
    {
        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->deleteJson('/api/employees/' . $this->employee->id);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar']
                ])
                ->assertJson([
                    'success' => true
                ]);

        $this->assertDatabaseMissing('users', [
            'id' => $this->employee->id
        ]);
    }

    public function test_employee_cannot_access_employee_management()
    {
        $token = $this->employee->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/employees');

        $response->assertStatus(403);
    }

    public function test_cannot_delete_last_admin()
    {
        // Make sure there's only one admin
        User::where('role', 'admin')->where('id', '!=', $this->admin->id)->delete();

        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->deleteJson('/api/employees/' . $this->admin->id);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false
                ]);

        $this->assertDatabaseHas('users', [
            'id' => $this->admin->id
        ]);
    }
}
