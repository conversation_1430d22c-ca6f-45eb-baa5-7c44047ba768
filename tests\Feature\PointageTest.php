<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PointageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create(['role' => 'employee']);
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->site = Site::factory()->create([
            'name' => 'Test Site',
            'latitude' => 33.5731,
            'longitude' => -7.5898
        ]);
    }

    public function test_user_can_check_location_within_range()
    {
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 33.5731, // Same as site
            'longitude' => -7.5898  // Same as site
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'is_within_range' => true
                    ]
                ]);
    }

    public function test_user_can_check_location_outside_range()
    {
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/check-location', [
            'site_id' => $this->site->id,
            'latitude' => 34.0209, // Different location (Rabat)
            'longitude' => -6.8416
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'is_within_range' => false
                    ]
                ]);
    }

    public function test_user_can_save_pointage()
    {
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/save-pointage', [
            'user_id' => $this->user->id,
            'site_id' => $this->site->id,
            'debut_pointage' => '2024-01-15 08:00:00',
            'fin_pointage' => '2024-01-15 17:00:00',
            'debut_latitude' => 33.5731,
            'debut_longitude' => -7.5898,
            'fin_latitude' => 33.5731,
            'fin_longitude' => -7.5898
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'id',
                        'user_id',
                        'site_id',
                        'debut_pointage',
                        'fin_pointage',
                        'duree'
                    ]
                ]);

        $this->assertDatabaseHas('pointages', [
            'user_id' => $this->user->id,
            'site_id' => $this->site->id
        ]);
    }

    public function test_admin_can_view_pointages()
    {
        // Create some pointages
        Pointage::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'site_id' => $this->site->id
        ]);

        $token = $this->admin->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/pointages');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'user_id',
                            'site_id',
                            'debut_pointage'
                        ]
                    ],
                    'pagination'
                ]);
    }

    public function test_employee_cannot_view_all_pointages()
    {
        $token = $this->user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/pointages');

        $response->assertStatus(403);
    }
}
