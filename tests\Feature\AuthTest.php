<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Log;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_login_with_valid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar'],
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ],
                        'token',
                        'token_type'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'token_type' => 'Bearer'
                    ]
                ]);

        // Verify successful login log was created
        $this->assertDatabaseHas('logs', [
            'user_id' => $user->id,
            'action' => 'login_attempt',
            'status' => 'success'
        ]);
    }

    public function test_user_cannot_login_with_invalid_credentials()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123')
        ]);

        $response = $this->postJson('/api/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        $response->assertStatus(401)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar']
                ])
                ->assertJson([
                    'success' => false
                ]);

        // Verify failed login log was created
        $this->assertDatabaseHas('logs', [
            'user_id' => $user->id,
            'action' => 'login_attempt',
            'status' => 'failed'
        ]);
    }

    public function test_login_validation_fails_with_invalid_email()
    {
        $response = $this->postJson('/api/login', [
            'email' => 'invalid-email',
            'password' => 'password123'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email']);
    }

    public function test_user_can_logout()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/logout');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message' => ['en', 'fr', 'ar']
                ])
                ->assertJson([
                    'success' => true
                ]);

        // Verify logout log was created
        $this->assertDatabaseHas('logs', [
            'user_id' => $user->id,
            'action' => 'logout_attempt',
            'status' => 'success'
        ]);
    }

    public function test_user_can_get_profile()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/me');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role'
                    ]
                ]);
    }
}
